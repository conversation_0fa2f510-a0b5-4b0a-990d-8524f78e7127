# 🤖 ButterGolem Discord Bot v6.0.0

[![Discord](https://img.shields.io/badge/Discord-Invite-7289DA?style=for-the-badge&logo=discord&logoColor=white)](https://discord.com/oauth2/authorize?client_id=1329104199794954240)
[![Ko-fi](https://img.shields.io/badge/Ko--fi-Support-FF5E5B?style=for-the-badge&logo=ko-fi&logoColor=white)](https://ko-fi.com/ninjazan420)
[![Version](https://img.shields.io/badge/Version-6.0.0-brightgreen?style=for-the-badge)](https://github.com/ninjazan420/buttergolem)

## 🎉 Meddl Loide! Das große 6.0.0 Update ist da!

**ButterGolem** ist zurück mit dem größten Update aller Zeiten! Komplett überarbeitet, modernisiert und mit hunderten neuen Features. Der Drachenlord Discord Bot, der dir zufällige Zitate vom Arschgebirge aus der Schimmelschanze direkt in deinen Discord-Server scheißt.

---

## 🚀 Was ist neu in Version 6.0.0?

### ⚡ **Komplette Modernisierung**
- **100% Slash Commands** - Alle Befehle wurden auf moderne `/` Commands umgestellt
- **Keine Intents mehr** - Bot funktioniert jetzt ohne privilegierte Berechtigungen
- **Bessere Performance** - Optimiert für Server mit 100+ Mitgliedern
- **Modulare Architektur** - Sauberer, wartbarer Code

### 🎭 **Erweiterte Drachenlord KI**
- **Massiv erweiterte Lore** - Hunderte neue Zitate, Ereignisse und Persönlichkeitsmerkmale
- **2024/2025 Updates** - Aktuelle Ereignisse und Kontroversen
- **Verbesserte Persönlichkeit** - Noch authentischere Antworten
- **Neue Reaktionsmuster** - Dynamische Antworten basierend auf Kontext

### 🔊 **Sound-System Upgrade**
- **500+ Soundclips** - Noch mehr GESCHREI und Cringe-Momente
- **Neue Kategorien** - Bessere Organisation der Sounds
- **Verbesserte Qualität** - Optimierte Audio-Dateien
- **Smart Playback** - Intelligente Sound-Auswahl

### 📊 **Erweiterte Statistiken**
- **Neofetch-Style Stats** - Coole ASCII-Art Statistiken
- **Animierte Displays** - Drachenlord und Shrek Themes
- **Detaillierte Metriken** - Server, User und Bot-Statistiken
- **Memory-System** - Persistente Nutzer-Erinnerungen

### 🛡️ **Admin-Features**
- **Server-Management** - Erweiterte Server-Verwaltung
- **Ban-System** - User und Server Bans
- **Debug-Tools** - Umfangreiche Debugging-Funktionen
- **Global Messaging** - Nachrichten an alle Server

---

## 🎮 Slash Commands Übersicht

### 👥 **Für alle Nutzer**

| Command | Beschreibung |
|---------|-------------|
| `/hilfe` | Zeigt alle verfügbaren Befehle |
| `/drache stats` | Bot-Statistiken anzeigen |
| `/sound [name]` | Spezifischen Sound abspielen |
| `/sounds` | Liste aller verfügbaren Sounds |
| `/lord` | Zufälligen Drachenlord Sound abspielen |
| `/mett` | Aktueller Mett-Level mit Meter |
| `/zitat` | Zufälliges Drachenlord Zitat |
| `/lordmeme [text] [position]` | Drachenlord Meme erstellen (Position: oben/unten/beide) |
| `/lordstats [@user]` | Lustige Statistiken für User |
| `/lordupdate` | Neueste Bot-Updates |
| `/quiz [runden]` | Drachenlord Quiz starten |
| `/ping` | Bot-Latenz anzeigen |
| `/kontakt [nachricht]` | Nachricht an Admin senden |
| `/privacy` | Datenschutzerklärung |

### 🔧 **Admin Commands**

| Command | Beschreibung |
|---------|-------------|
| `/drache neofetch` | Animierte Bot-Stats (neofetch-Style) |
| `/drache drachenlord` | Stats mit Drachenlord-Zitaten |
| `/drache shrek` | Stats mit Shrek-Theme |
| `/memory list` | Alle Nutzer-Erinnerungen |
| `/memory show [user]` | Spezifische Erinnerung |
| `/memory add [user] [text]` | Neue Erinnerung hinzufügen |
| `/memory delete [user]` | Erinnerung löschen |
| `/servercount` | Manueller Server-Counter Update |
| `/server [page]` | Server-Liste & Statistiken |
| `/antwort [id] [text]` | Antwort an Nutzer |
| `/debug_sounds` | Sound-System Debug |
| `/butteriq [action]` | KI-Zugriff verwalten |
| `/global [nachricht]` | Globale Nachricht senden |

---

## 🤖 Drachenlord KI Chat

**Chatte direkt mit dem Drachenlord!**

- **@ButterGolem [nachricht]** - Erwähne den Bot in einem Channel
- **DM** - Schreibe dem Bot eine private Nachricht

### 🧠 **KI Features:**
- Authentische Drachenlord Persönlichkeit
- Kontextbewusste Antworten
- Aktuelle Ereignisse und Memes
- Dynamische Reaktionen
- Kostenlos für alle Nutzer

---

## 🔊 Sound-System

### 🎵 **500+ Soundclips verfügbar:**
- **Klassisches GESCHREI** - Die besten Drachenlord Momente
- **Cringe-Sounds** - Für besonders peinliche Momente
- **Neue 2024 Clips** - Frische Sounds aus aktuellen Streams
- **Kategorisiert** - Einfach zu finden und zu verwenden

### 🎧 **Sound Commands:**
- `/sounds` - Durchblätterbare Liste aller Sounds
- `/sound [name]` - Spezifischen Sound abspielen
- `/lord` - Zufälligen Sound abspielen

---

## ❓ Quiz-System

**Teste dein Drachenlord Wissen!**

- **150+ Fragen** über Drachenlord, Haider und die Schanze
- **Verschiedene Schwierigkeitsgrade** 
- **Multiplayer-Support** - Spiele mit Freunden
- **Statistiken** - Verfolge deine Erfolge

**Quiz starten:** `/quiz [anzahl_runden]` (1-20 Runden)

---

## 📊 Statistiken & Neofetch

### 🖥️ **Neofetch-Style Displays:**
```
/drache neofetch     → Standard ASCII-Art Stats
/drache drachenlord  → Mit Drachenlord Zitaten
/drache shrek        → Shrek-Theme Stats
```
## 🥦 Admin Commands
- Neu: Global um globale Nachrichten an die Join Channel zu schicken
- Neu: Memory-System um Nutzer-Erinnerungen zu speichern
- Neu: Server-Liste um alle Server anzuzeigen
- Neu: Server-Statistiken um Server-Details anzuzeigen
- Neu: Server-Counter um Serveranzahl zu aktualisieren

### 📈 **Verfügbare Metriken:**
- Server-Anzahl und Nutzer
- Uptime und Performance
- Sound-Statistiken
- Memory-Usage
- Bot-Version und Features

---

## 🛠️ Installation & Setup

### 🐳 **Docker Installation (Empfohlen)**

1. **Repository klonen:**
```bash
git clone https://github.com/ninjazan420/buttergolem.git
cd buttergolem
```

2. **Environment Variables setzen:**
```env
DISCORD_API_TOKEN=dein_bot_token
LOGGING_CHANNEL=channel_id_für_logs
ADMIN_USER_ID=deine_discord_user_id
BLACKLISTED_GUILDS=server_id1,server_id2
ENABLE_RANDOM_JOINS=False
```

3. **Bot starten:**
```bash
docker compose build
docker compose up -d
```

4. **Logs anzeigen:**
```bash
docker compose logs -f
```

### 🐍 **Manuelle Installation**

1. **Python 3.8+ installieren**

2. **Dependencies installieren:**
```bash
pip install discord.py psutil requests aiohttp beautifulsoup4 Pillow
```

3. **Bot starten:**
```bash
cd src
python main.py
```

### ⚙️ **Bot-Berechtigungen**

Der Bot benötigt folgende Berechtigungen:
- Nachrichten senden
- Slash Commands verwenden
- Voice Channels beitreten
- Voice Channels sprechen
- Dateien anhängen
- Embeds senden

---

## 🏗️ Modulare Architektur

Der Bot ist vollständig modular aufgebaut:

```
src/
├── main.py              # Hauptdatei und Bot-Setup
├── slash_commands.py    # Alle Slash Commands
├── sounds.py           # Sound-System
├── ki.py               # KI-Integration
├── quiz.py             # Quiz-System
├── lordstats.py        # User-Statistiken
├── lordmeme.py         # Meme-Generator
├── memory.py           # Memory-System
├── admins/             # Admin-Funktionen
│   ├── admin_commands.py
│   ├── ban_manager.py
│   ├── stats_manager.py
│   └── server_list_view.py
├── ki/                 # KI-Daten
│   ├── drache.json
│   ├── drache_lore.json
│   ├── drache_events_2024_2025.json
│   └── ...
└── data/               # Bot-Daten
    ├── sounds/
    ├── memories/
    └── stats.json
```

### 🔧 **Neue Module hinzufügen:**

1. Erstelle `src/mein_modul.py`
2. Implementiere `register_commands(bot)` Funktion
3. Importiere in `main.py`
4. Registriere mit `register_commands(client)`

---

## 🔄 Migration von v5.x

### ⚠️ **Breaking Changes:**
- **Alle Prefix Commands entfernt** - Nur noch Slash Commands
- **Intents entfernt** - Bot benötigt keine privilegierten Berechtigungen
- **Neue Command-Struktur** - Alle Commands unter `/drache` oder eigenständig
- **Geänderte Admin-Commands** - Neue Syntax und Parameter

### 📋 **Migrations-Checklist:**
- [ ] Bot-Token aktualisieren
- [ ] Neue Berechtigungen setzen
- [ ] Slash Commands aktivieren
- [ ] Admin-IDs konfigurieren
- [ ] Logging-Channel setzen

---

## 💝 Support & Spenden

**Unterstütze die Entwicklung:**

[![Ko-fi](https://img.shields.io/badge/Ko--fi-Support-FF5E5B?style=for-the-badge&logo=ko-fi&logoColor=white)](https://ko-fi.com/buttergolem)
[![Support](https://img.shields.io/badge/Discord-Join-7289DA?style=for-the-badge&logo=discord&logoColor=white)](https://discord.gg/4kHkaaS2wq)

### 🐞 **Bug Reports:**
Probleme bitte als [GitHub Issue](https://github.com/ninjazan420/buttergolem/issues) melden.

- **Ko-fi Spenden** - Einmalige Unterstützung
- **Feature Requests** - Neue Ideen und Vorschläge
- **Bug Reports** - Hilf uns Fehler zu finden
- **Community Support** - Hilfe von anderen Nutzern

---

## 📜 Datenschutz & Rechtliches

- **[Datenschutzerklärung](privacy_policy.md)** - Vollständige Privacy Policy
- **Datensammlung:** Minimal - nur notwendige Bot-Funktionen
- **Keine Speicherung** von Nachrichten oder persönlichen Daten
- **DSGVO-konform** - Europäische Datenschutzstandards

**Datenschutz anzeigen:** `/privacy`

---

## 🐛 Bekannte Issues & Roadmap

### 🔧 **Aktuell in Arbeit:**
- [ ] Weitere Sound-Kategorien
- [ ] Erweiterte Quiz-Modi
- [ ] Custom Server-Einstellungen
- [ ] Web-Dashboard

---

## 👨‍💻 Entwickler

**Created by:** [ninjazan420](https://github.com/ninjazan420)

---

## 📄 Lizenz

Dieses Projekt steht unter der [GPL v3](LICENSE).

---

**🎉 Viel Spaß mit ButterGolem v6.0.0! Meddl Loide! 🎉**

*"Ich bin nicht der Messias, ich bin ein sehr ungezogener Junge!"* - Drachenlord, 2024
